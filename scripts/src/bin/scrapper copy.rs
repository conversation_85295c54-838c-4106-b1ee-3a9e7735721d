#![feature(
    closure_lifetime_binder,
    coroutines,
    coroutine_trait,
    iterator_try_collect,
    // generic_const_exprs,
    // generic_const_items,
    // non_lifetime_binders,
    step_trait,
    stmt_expr_attributes,
    thread_id_value,
    try_blocks,
    type_alias_impl_trait,
)]

/// Scrap breeds from AKC and Purina UK websites and save them into a JSON file.
///
///
/// Here is the exact plan, do not add, remove or change anything:
///
///   (1) Access the American Kennel Club (AKC) website by iterating through pages from 1 to 25 (<https://www.akc.org/dog-breeds/page/{PAGE_NUMBER>}/, for example, for page 12 URL is <https://www.akc.org/dog-breeds/page/12>/) to compile a comprehensive list of dog breeds. Every page contains 12 breeds except the last one which has only 2, so in total we have 292 breeds.
///
///   (2) On the ACL website breeds list pages have a set of cards with a short breed information (breed image, name, description preview and "See More" link). Breed cards start after the alphabet section and are wrapped with a div with "breed-card-type-grid" class. Every card is wrapped in a div with "breed-type-card" class. The "See More" is a link to the breed details page (example, <https://www.akc.org/dog-breeds/manchester-terrier-standard>/ for "Manchester Terrier (Standard)").
///
///   (3) For each breed identified from the AKC website, extract breed name and image, then navigate to its dedicated breed details page (e.g., <https://www.akc.org/dog-breeds/nova-scotia-duck-tolling-retriever>/) and extract all basic, advanced and extra breed characteristics and details. Basic information (height, weight, life expectancy) is located in the top of the page right below the breed video/image section and is wrapped with a div with "breed-page__hero__overview__icon-block-wrap" class. Advanced information is presented below the basic one under the 'All Traits' tab (is accessible with a click on a respective tab title and is within a div with "breed-page__traits__all" Id in the page html code). Every advanced trait is in a div with the "breed-trait-group__trait breed-trait-group__padded breed-trait-group__row-wrap" class. Some details are collapsed by default and are accessible by tapping on "Read more" or a plus icon. Extra information pieces are in divs with a "breed-table__wrap" class. The content is available (appears with a help of JavaScript) only when clicking a plus icon for every details row. Without JavaScript extra details could be extracted from a "data-js-props" attribute value of a div with an attribute "data-js-component" with a value "breedPage".
///
///   (4) Access the Purina UK website by iterating through pages from 0 to 19 (<https://www.purina.co.uk/find-a-pet/dog-breeds?page=%2C{PAGE_NUMBER>}, for example, for page 17 URL i<https://www.purina.co.uk/find-a-pet/dog-breeds?page=%2C17>17) to compile a comprehensive list of dog breeds. Every page contains 12 breeds. 20 pages gives us 240 breeds in total. While URLs are numbered from 0 to 19, for page visitors pages are numbered from 1 to 20. This is an expected behavior.
///
///   (5) On the Purina UK website breeds list pages have a set of cards with a short breed information
/// (breed image and name). Every such a card itself is a link to a breed details page.
///
///   (6) For each breed identified from the Purina UK website, extract breed name and image, then navigate to its dedicated detail page (e.g., <https://www.purina.co.uk/find-a-pet/dog-breeds/mexican-hairless-medium>) and extract a breed description and all available basic and advanced breed characteristics and details. Decscription is in the top of the page below the breed image and name and is wrapped in a div with the "field--name-field-nppe-bs-description" class. Basic details go right below the description and are wrapped in a div with the "field--name-field-key-facts" class, each detail is in a separate div with the "key-facts-item" class. Advanced details wrapped in a div with the "field--name-field-c-subitems" class, each detail is in a separate div with the `field__item` class.
///
///   (7) Consolidate the lists of breed names from both sources, creating a master list of unique
/// breeds, ensuring that breeds found on only one website are also included.
///
///   (8) For each unique breed, synthesize all extracted details from both AKC and Purina, combining
/// and deduplicating any repeated information, and retaining all known details even if a breed is
/// present on only one source. In a case of details conflict between AKC and Purina, keep information
/// from both sources.
///
///   (9) Organize the consolidated and deduplicated breed data into a Parquet file, with each breed
/// name in the first column, followed by a columns with all identified breed traits and
/// characteristics. Put column titles in the first table row.
use std::collections::{
    BTreeMap,
    HashSet,
};
use std::{
    fmt::Display,
    ops::Deref,
    sync::{
        Arc,
        LazyLock,
    },
};

use async_recursion::async_recursion;
use futures::future::FutureExt;
use hashlink::LinkedHashMap;
use headless_chrome::{
    Browser,
    Element as DynamicElement,
    Tab,
};
use polars::prelude::IntoVec;
use regex::Regex;
// use polars::prelude::*;
use scraper::{
    ElementRef as StaticElement,
    Html,
    Selector,
};
// use serde::Serialize;
use slog::{
    Drain,
    debug,
    error,
    info,
};
use thiserror::Error;
use tokio::{
    task::JoinSet,
    time::Instant,
};
use yaml_rust2::{
    Yaml,
    YamlEmitter,
    YamlLoader,
};


/// Global logger
static LOG: LazyLock<slog::Logger> = LazyLock::new(|| {
    let decorator = slog_term::PlainSyncDecorator::new(std::io::stdout());
    slog::Logger::root(slog_term::FullFormat::new(decorator).build().fuse(), slog::o!())
});
static DRIVER: LazyLock<Arc<Browser>> =
    LazyLock::new(|| Arc::new(Browser::default().expect("Failed to connect to browser")));
const AKC_BREEDS_PAGES: u16 = 25;
const PURINA_BREEDS_PAGES: u16 = 20;


// * Models

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct Breed {
    name:        String,
    details_url: String,
    image_url:   String,
}

impl Display for Breed {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}: details: {}, image: {}", self.name, self.details_url, self.image_url)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct BreedDetails {
    name:             String,
    image_url:        String,
    description:      String,
    basic_details:    BTreeMap<String, String>,
    advanced_details: BTreeMap<String, String>,
}

#[derive(Debug, Clone, PartialEq)]
enum ScrappedData {
    Vec(Vec<Self>),
    Map(LinkedHashMap<String, Self>),
    String(String),
    Float(f32),
    Int(i32),
}
impl Display for ScrappedData {
    fn fmt(&self, fmt: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Vec(vec) => {
                write!(fmt, "[")?;
                write!(
                    fmt,
                    "{}",
                    vec.iter()
                        .map(|item| format!("{item}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "]")
            },
            Self::Map(map) => {
                write!(fmt, "{{")?;
                write!(
                    fmt,
                    "{}",
                    map.iter()
                        .map(|(k, v)| format!("\"{k}\": {v}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "}}")
            },
            Self::String(s) => write!(fmt, "\"{s}\""),
            Self::Float(f) => write!(fmt, "{f:.1}"),
            Self::Int(i) => write!(fmt, "{i}"),
        }
    }
}


#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
enum ElementType {
    Float,
    Int,
    List,
    Map,
    String,
    Text,
    Url,
}
impl ElementType {
    fn from_str(s: &str) -> Self {
        match s {
            "float" => Self::Float,
            "int" => Self::Int,
            "list" => Self::List,
            "map" => Self::Map,
            "text" => Self::Text,
            "url" => Self::Url,
            _ => Self::String,
        }
    }
}

// * Main

// #[tokio::main(worker_threads = 32)]
#[tokio::main]
async fn main() {
    info!(LOG, "Starting.\n");

    let yaml = std::fs::read_to_string("breeds.yaml").unwrap();
    let rules = YamlLoader::load_from_str(&yaml).unwrap();
    let rule = rules.first().unwrap();

    let now = Instant::now();
    let results = scrap_by_rules(rule).await;
    info!(LOG, "Scraped in {:.0?}", now.elapsed());
    debug!(LOG, "Results: {}", results);

    // info!(LOG, "Scraping AKC breeds (ureq blocking).");
    // let now = Instant::now();
    // let breeds_akc = scrap_list_from_static_pages(
    //     "https://www.akc.org/dog-breeds/page/{page}/",
    //     1,
    //     AKC_BREEDS_PAGES,
    //     extract_breeds_akc,
    // )
    // .await;
    // info!(LOG, "Found {} breeds on AKC [{:.0?}].\n", &breeds_akc.len(), now.elapsed());

    // info!(LOG, "Scraping AKC breed details.");
    // let now = Instant::now();
    // let breed_details_akc =
    //     scrap_from_dynamic_page(breeds_akc, |breed: &Breed| &breed.details_url,
    // extract_akc_breed_details)         .await
    //         .unwrap();
    // // breed_details_akc.sort_by(|b1, b2| b1.name.cmp(&b2.name));
    // info!(
    //     LOG,
    //     "Scraped {} breed details on AKC [{:.0?}].\n",
    //     breed_details_akc.len(),
    //     now.elapsed()
    // );

    // info!(LOG, "Scraping Purina breeds.");
    // let now = Instant::now();
    // let breeds_purina = scrap_list_from_static_pages(
    //     "https://www.purina.co.uk/find-a-pet/dog-breeds?page=%2C{page}",
    //     0,
    //     PURINA_BREEDS_PAGES - 1,
    //     extract_breeds_purina,
    // )
    // .await;
    // info!(
    //     LOG,
    //     "Found {} breeds on Purina [{:.0?}].\n",
    //     &breeds_purina.len(),
    //     now.elapsed()
    // );

    // info!(LOG, "Scraping Purina breed details.");
    // let now = Instant::now();
    // let breed_details_purina = scrap_from_static_page(
    //     breeds_purina,
    //     |breed: &Breed| &breed.details_url,
    //     extract_purina_breed_details,
    // );
    // info!(
    //     LOG,
    //     "Found {} breeds on Purina [{:.0?}].\n",
    //     breed_details_purina.len(),
    //     now.elapsed()
    // );

    // info!(LOG, "Consolidating breeds.");
    // let breeds_consolidated = consolidate_breeds(&breeds_akc, &breeds_purina);
    // info!(LOG, "Found {} unique breeds.\n", breeds_consolidated.len());

    // info!(LOG, "Getting all breed details.");
    // let all_breed_details: HashSet<BreedDetails> = breed_details_akc
    //     .union(&breed_details_purina)
    //     .cloned()
    //     .collect();
    // info!(LOG, "Found {} breeds in total.\n", all_breed_details.len());

    // info!(LOG, "Consolidating breed details.");
    // let breeds = consolidate_breed_details(all_breed_details);
    // info!(LOG, "Found {} breeds in total.\n", breeds.len());

    // info!(LOG, "Saving breeds to Parquet.");
    // save_breeds_to_parquet(breed_details_akc);
    info!(LOG, "Done.");
}


// * Scrapers


#[derive(Debug, Error)]
pub enum GetError {
    #[error("Max retries exceeded.")]
    MaxRetries(#[from] ureq::Error),
}

fn get_page_body(url: &str) -> Result<Html, GetError> {
    const MAX_RETRIES: usize = 3;

    let mut agent: ureq::Agent = ureq::Agent::config_builder()
        .timeout_per_call(Some(std::time::Duration::from_millis(3000)))
        .build()
        .into();
    let mut retries = 0;
    loop {
        match agent.get(url).call() {
            Ok(mut response) => match response.body_mut().read_to_string() {
                Ok(mut body) => {
                    return Ok(Html::parse_document(&body));
                },
                Err(err) => {
                    if retries >= MAX_RETRIES {
                        return Err(GetError::MaxRetries(err));
                    }
                    retries += 1;
                },
            },
            Err(err) => {
                if retries >= MAX_RETRIES {
                    return Err(GetError::MaxRetries(err));
                }
                retries += 1;
            },
        }
    }
}

async fn scrap_list_from_static_pages_range<R: Clone + Send + Sync + 'static>(
    url_format: &str,
    start_page: u16,
    end_page: u16,
    extractor: fn(&Html) -> Vec<R>,
) -> Vec<R> {
    let mut tasks = JoinSet::new();
    for page in start_page..=end_page {
        let url_format = url_format.to_string();
        tasks.spawn_blocking(move || {
            #[allow(clippy::literal_string_with_formatting_args)]
            extractor(&get_page_body(&url_format.replace("{page}", &page.to_string())).unwrap())
        });
    }

    tasks.join_all().await.concat()
}

async fn scrap_with_items_from_dynamic_page<I: Send + 'static, R: Send + 'static>(
    items: Vec<I>,
    get_url: fn(&I) -> &str,
    extractor: fn(&Arc<Tab>, &I) -> Result<R, anyhow::Error>,
) -> Result<Vec<R>, anyhow::Error> {
    let driver = Browser::default()?; // headless_chrome

    let mut tasks = JoinSet::new();
    for item in items {
        let driver = driver.clone();
        tasks.spawn_blocking(move || {
            let tab = driver.new_tab().unwrap();
            tab.navigate_to(get_url(&item)).unwrap();
            tab.wait_until_navigated();
            let result = extractor(&tab, &item);
            tab.close(false).unwrap();

            result.unwrap()
        });
    }

    Ok(tasks.join_all().await)
}

fn scrap_with_items_from_static_page<I: Send + 'static, R: Send + 'static>(
    items: Vec<I>,
    get_url: fn(&I) -> &str,
    extractor: fn(&Html, &I) -> Option<R>,
) -> Vec<R> {
    let mut results = Vec::new();
    for item in items {
        results.push(extractor(&get_page_body(get_url(&item)).unwrap(), &item).unwrap());
    }
    results
}


const RESERVED_KEYS: [&str; 11] = [
    "name",
    "url",
    "kind",
    "page",
    "selector",
    "children",
    "key",
    "value",
    "attribute",
    "follow",
    "prefix",
];
/// Function that recursively scrapes data from website(s) based on a set of rules.
/// The rules are defined in a YAML file.
/// Example of the rules could be found in the `breeds.yaml` file.
/// When a `children` key is present in the rule, the function is called recursively.
/// When `url` key is present in the rule and `follow` key is set to `true`, the function scrapes the data
/// from the website. When `key` and `value` keys are present in the rule, the function extracts
/// all key/value pairs from the HTML element that matches the selector.
/// If `attribute` key is present, the function extracts the value of the attribute.
/// If a placeholder is present in the `url` key (e.g. `{page}`), the function
/// scrapes the data from multiple pages by replacing the placeholder with the page number.
/// A single value, a set of values of a range would be provided in the YAML key
/// with a respective to a placeholder name.
/// Available data types defined in the `type` key: `string`, `int`, `float`,
/// `text`. `text` value corresponds to the multiline text and can be collected
/// from nested <p> tags or, if there are no <p> tags, from the `text()`.
/// All processing is done using multithreading with `spawn_blocking`.
/// `kind` key defines the type of the scraping: `static` or `dynamic`. `static`
/// scraping is done using `scrap_from_static_page` function for single pages  and
/// `scrap_list_from_static_pages` function for lists of pages, `dynamic` - using
/// `scrap_from_dynamic_page` function for single pages and
/// `scrap_list_from_dynamic_pages` function for lists of pages.
#[allow(clippy::too_many_lines)]
#[async_recursion]
async fn scrap_by_rules(rule: &Yaml) -> Arc<ScrappedData> {
    // debug!(LOG, "Processing rule: {:?}", rule);
    if rule.is_array() {
        let rule_vec = rule.as_vec().unwrap();
        let mut tasks = JoinSet::new();
        for r in rule_vec {
            let r = r.clone();
            tasks.spawn(async move { scrap_by_rules(&r).await });
        }
        let items = tasks.join_all().await;

        let mut results = LinkedHashMap::new();
        for item in items {
            if let ScrappedData::Map(map) = item.as_ref().to_owned() {
                // debug!(LOG, "Extending map: {:?}\n", map);
                results.extend(map);
            }
        }

        return Arc::new(ScrappedData::Map(results));
    }

    if !rule.is_hash() {
        return Arc::new(ScrappedData::String("--".to_string()));
    }

    let rule_as_map = rule.as_hash().unwrap();

    // * If is a list entry return as a map
    let first_value = rule_as_map.values().next().unwrap();
    if rule_as_map.len() == 1 && first_value.is_hash() {
        return Arc::new(ScrappedData::Map(LinkedHashMap::from_iter([(
            rule_as_map
                .keys()
                .next()
                .unwrap()
                .as_str()
                .unwrap()
                .to_string(),
            scrap_by_rules(first_value).await.as_ref().clone(),
        )])));
    }
    // return Arc::new(ScrappedData::Map(LinkedHashMap::from([(
    //     key.as_str().unwrap().to_string(),
    //     scrap_by_rules(value).await,
    // )])));

    // let mut results = LinkedHashMap::new();
    // for (key, value) in rule_hash {
    //     // if key.as_str().unwrap() == "name" {
    //     //     debug!(LOG, "Processing rule: {:?}", value);
    //     // }
    //     results.insert(key.as_str().unwrap().to_string(),
    // scrap_by_rules(value).await.as_ref().clone()); }
    // debug!(LOG, "Map: ({}) {:?}", results.len(), results);

    // let first_rule = rule_hash.values().next().unwrap();
    // if results.len() == 1 && first_rule.is_hash() {
    //     return Arc::new(ScrappedData::Map(LinkedHashMap::from_iter([(
    //         results.keys().next().unwrap().to_owned(),
    //         results.values().next().unwrap().to_owned(),
    //     )])));
    // }

    // return Arc::new(ScrappedData::Map(results));
    // return Arc::new(ScrappedData::String("yo".to_string()));


    // // let node_name = rules[0]["name"].as_str().unwrap_or_default();

    // * Check if we have an explicit URL to scrap
    if !rule["url"].is_badvalue() {
        let url = rule["url"].as_str().unwrap();

        // * Check if we have placeholders in the URL by extracting all text between `{` and `}`
        let placeholders: Vec<&str> = Regex::new(r"\{(.*?)\}")
            .unwrap()
            .captures(url)
            .map(|caps| {
                let count = caps.len() - 1;
                (0..count)
                    .map(move |i| caps.get(i + 1).unwrap().as_str())
                    .collect()
            })
            .unwrap_or_default();

        // * Get placeholder values from the rule, could be a set of values or a range
        // let placeholder_values: LinkedHashMap<String, Box<dyn Iterator<Item = String> + Send>> =
        // let placeholder_values: LinkedHashMap<String, dyn Iterator<Item = String>> =
        // let placeholder_values: LinkedHashMap<String, Iter> =
        let mut placeholder_values = placeholders
            .into_iter()
            .map(move |placeholder| {
                let placeholder_rule = &rule[placeholder];
                assert!(!placeholder_rule.is_badvalue(), "Placeholder {placeholder} has no value");
                if placeholder_rule.is_array() {
                    return (
                        placeholder,
                        placeholder_rule
                            .as_vec()
                            .unwrap()
                            .iter()
                            .map(|v| v.as_str().unwrap().to_string())
                            .collect(),
                    );
                } else if let Some(value) = placeholder_rule.as_str() {
                    let values = value.split("..").collect::<Vec<&str>>();
                    let start: u16 = values[0].parse().unwrap();
                    let end: u16 = values[1].parse().unwrap();
                    return (placeholder, (start..=end).map(|i| i.to_string()).collect());
                }
                panic!("Unknown placeholder value type for {placeholder}");
            })
            .collect::<LinkedHashMap<&str, Vec<String>>>();
        // debug!(LOG, "Placeholder values: {:?}", &placeholder_values);

        // * Get all possible URL combinations
        let mut urls = vec![url.to_string()];
        for (placeholder, values) in placeholder_values {
            let mut new_urls = Vec::new();
            for value in values {
                for url in &urls {
                    new_urls.push(url.replace(&format!("{{{placeholder}}}"), &value));
                }
            }
            urls = new_urls;
        }
        // debug!(LOG, "URLs: {:?}", urls);

        let is_dynamic = rule["kind"].as_str().unwrap_or_default() == "dynamic";
        let mut tasks = JoinSet::new();
        for url in urls {
            let rule = rule.clone();
            tasks.spawn_blocking(move || {
                let mut html: Option<Html> = None;
                if is_dynamic {
                    let tab = DRIVER.new_tab().unwrap();
                    tab.navigate_to(&url).unwrap();
                    tab.wait_until_navigated();
                    html = Some(Html::parse_document(&tab.get_content().unwrap()));
                    tab.close(false).unwrap();
                } else {
                    html = Some(get_page_body(&url).unwrap());
                }
                if let Some(html) = html {
                    futures::executor::block_on(async {
                        extract_by_rules_static(&html.root_element(), &rule).await
                    })
                } else {
                    panic!("Failed to get HTML for {url}");
                }
            });
        }
        let results = tasks.join_all().await;
        return Arc::new(ScrappedData::Vec(
            if !results.is_empty() && matches!(results[0], ScrappedData::Vec(_)) {
                results
                    .into_iter()
                    .filter_map(|r| match r {
                        ScrappedData::Vec(v) => Some(v),
                        _ => None,
                    })
                    .flatten()
                    .collect()
            } else {
                results
            },
        ));

        // if !rule["page"].is_badvalue() {
        //     let page = rule["page"].as_str().unwrap();
        //     if rule["kind"].as_str().unwrap() == "static" {
        //         return scrap_list_from_static_pages(
        //             url,
        //             page.parse().unwrap(),
        //             page.parse().unwrap(),
        //             |_: &Html| vec![],
        //         )
        //         .await;
        //     } else {
        //         return scrap_list_from_dynamic_pages(
        //             url,
        //             page.parse().unwrap(),
        //             page.parse().unwrap(),
        //             |_: &Arc<Tab>| Ok(()),
        //         )
        //         .await
        //         .unwrap();
        //     }
        // }

        // if rule["kind"].as_str().unwrap_or_default() == "dynamic" {
        //     return scrap_from_dynamic_page(vec![], |_: &()| url, |_: &Arc<Tab>, _: &()| Ok(()))
        //         .await
        //         .unwrap();
        // } else {
        //     return scrap_from_static_page(vec![], |_: &()| url, |_: &Html, _: &()| None);
        // }
    }

    // * Decide if we need to scrap the data or just save the URL
    rule["follow"].is_badvalue();
    // if !rule["selector"].is_badvalue() {
    //     if rule["kind"].as_str().unwrap() == "static" {
    //         return scrap_from_static_page(vec![], |_: &()| url, |_: &Html, _: &()| None);
    //     } else {
    //         return scrap_from_dynamic_page(vec![], |_: &()| url, |_: &Arc<Tab>, _: &()| Ok(()))
    //             .await
    //             .unwrap();
    //     }
    // }
    // debug!(LOG, "\n");

    let has_children =
        !rule["children"].is_badvalue() && (rule["children"].is_hash() || rule["children"].is_array());
    if has_children {
        // let mut tasks = JoinSet::new();
        // for (key, value) in children_map {
        //     let key = key.clone();
        //     let value = value.clone();
        //     tasks.spawn(async move { scrap_by_rules(&value).await });
        // }
        // let items = tasks.join_all().await;

        // let mut results = LinkedHashMap::new();
        // for item in items {
        //     if let ScrappedData::Map(map) = item.as_ref().to_owned() {
        //         // debug!(LOG, "Extending map: {:?}\n", map);
        //         results.extend(map);
        //     }
        // }

        return Arc::clone(&scrap_by_rules(&rule["children"]).await);
    }

    Arc::new(ScrappedData::Map(LinkedHashMap::new()))
}

#[allow(clippy::too_many_lines)]
async fn extract_by_rules_static(parent: &StaticElement, rule: &Yaml) -> ScrappedData {
    if !rule["selector"].is_badvalue() {
        let selector = rule["selector"].as_str().unwrap();
        let element_type = if !rule["key"].is_badvalue() {
            ElementType::Map
        } else if !rule["type"].is_badvalue() {
            ElementType::from_str(rule["type"].as_str().unwrap())
        } else {
            ElementType::String
        };
        // debug!(LOG, "Selector: {} ({:?})", selector, element_type);
        let query = Selector::parse(selector).unwrap();
        let mut element_selector = parent.select(&query);
        let element = element_selector.next().unwrap();
        let text = element.text().next().unwrap_or_default().trim().to_string();
        return match element_type {
            ElementType::List => {
                let results = if rule["children"].is_badvalue() {
                    vec![]
                } else {
                    element_selector
                        .map(|element| extract_by_rules_static(&element, &rule["children"]))
                        .await
                        .collect()
                };

                ScrappedData::Vec(results)
            },
            ElementType::Map => {
                let key = element
                    .select(&Selector::parse(&rule["key"].as_str().unwrap().trim().to_lowercase()).unwrap())
                    .next()
                    .unwrap()
                    .text()
                    .next()
                    .unwrap()
                    .trim()
                    .to_string();
                let value = extract_by_rules_static(&element, &rule["value"]).await;
                ScrappedData::Map(LinkedHashMap::from_iter([(key, value)]))
            },
            ElementType::Text => ScrappedData::String(
                element_selector
                    .map(|element| {
                        let text = element.text().next().unwrap().trim().to_string();
                        if text.is_empty() {
                            element
                                .select(&Selector::parse("p").unwrap())
                                .next()
                                .unwrap()
                                .text()
                                .next()
                                .unwrap()
                                .trim()
                                .to_string()
                        } else {
                            text
                        }
                    })
                    .collect(),
            ),
            ElementType::Url => {
                let attribute = rule["attribute"].as_str().unwrap_or("href");
                let url = element
                    .value()
                    .attr(attribute)
                    .unwrap_or_default()
                    .to_string()
                    .to_lowercase();

                if url.starts_with("http") && rule["follow"].as_bool().unwrap_or_default() {
                    let rule = rule.clone();
                    let is_dynamic = rule["kind"].as_str().unwrap_or_default() == "dynamic";
                    tokio::task::spawn_blocking(move || {
                        let mut html: Option<Html> = None;
                        if is_dynamic {
                            let tab = DRIVER.new_tab().unwrap();
                            tab.navigate_to(&url).unwrap();
                            tab.wait_until_navigated();
                            html = Some(Html::parse_document(&tab.get_content().unwrap()));
                            tab.close(false).unwrap();
                        } else {
                            html = Some(get_page_body(&url).unwrap());
                        }
                        if let Some(html) = html {
                            futures::executor::block_on(async {
                                extract_by_rules_static(&html.root_element(), &rule).await
                            })
                        } else {
                            ScrappedData::String(url)
                        }
                    })
                    .await
                    .unwrap()
                } else {
                    ScrappedData::String(url)
                }
            },
            ElementType::Int | ElementType::Float => ScrappedData::Float(text.parse().unwrap()),
            _ => ScrappedData::String(text),
        };
    }
    if rule.is_array() {
        let mut results = LinkedHashMap::new();
        for r in rule.as_vec().unwrap() {
            let key = r
                .as_hash()
                .unwrap()
                .keys()
                .next()
                .unwrap()
                .as_str()
                .unwrap()
                .to_string();
            let value = extract_by_rules_static(parent, &r[key.as_str()]).await;
            results.insert(key, value);
        }
        return ScrappedData::Map(results);
    }
    ScrappedData::String(String::new())
}

fn extract_by_rules_dynamic(tab: &DynamicElement, rule: &Yaml) -> ScrappedData {
    if !rule["selector"].is_badvalue() {
        let selector = rule["selector"].as_str().unwrap();
        if !rule["key"].is_badvalue() {
            let key = rule["key"].as_str().unwrap();
            let value = rule["value"].as_str().unwrap();
            return ScrappedData::Vec(
                tab.find_elements(selector)
                    .unwrap()
                    .iter()
                    .map(|element| {
                        let key = element.find_element(key).unwrap().get_inner_text().unwrap();
                        let value = element
                            .find_element(value)
                            .unwrap()
                            .get_inner_text()
                            .unwrap();
                        ScrappedData::Map(LinkedHashMap::from_iter([(key, ScrappedData::String(value))]))
                    })
                    .collect(),
            );
        }
    }
    ScrappedData::String(String::new())
}


fn consolidate_breeds(set1: &HashSet<Breed>, set2: &HashSet<Breed>) -> HashSet<Breed> {
    let mut consolidated_breeds: HashSet<Breed> = HashSet::new();
    for breed in set1 {
        consolidated_breeds.insert(breed.clone());
    }
    for breed in set2 {
        consolidated_breeds.insert(breed.clone());
    }
    consolidated_breeds
}

fn consolidate_breed_details(breeds: HashSet<BreedDetails>) -> HashSet<BreedDetails> {
    let mut consolidated_breeds = HashSet::new();
    for breed in breeds {
        consolidated_breeds.insert(breed);
    }
    consolidated_breeds
}


fn extract_breeds_akc(body: &Html) -> Vec<Breed> {
    body.select(&Selector::parse(".breed-type-card").unwrap())
        .filter_map(|element| {
            let name = element
                .select(&Selector::parse("h3").unwrap())
                .next()?
                .text()
                .next()?
                .trim()
                .to_string();
            let details_url = element
                .select(&Selector::parse("a").unwrap())
                .next()?
                .value()
                .attr("href")?
                .to_string();
            let image_url = element
                .select(&Selector::parse("img").unwrap())
                .next()?
                .value()
                .attr("data-src")?
                .to_string();

            Some(Breed {
                name,
                details_url,
                image_url,
            })
        })
        .collect()
}

fn extract_breeds_purina(body: &Html) -> Vec<Breed> {
    body.select(&Selector::parse(".result-animal-container").unwrap())
        .filter_map(|element| {
            let name = element
                .select(&Selector::parse("h4 > a").unwrap())
                .next()?
                .text()
                .next()?
                .trim()
                .to_string();
            let details_url = element
                .select(&Selector::parse("a").unwrap())
                .next()?
                .value()
                .attr("href")?
                .to_string();
            let image_url = element
                .select(&Selector::parse("img").unwrap())
                .next()?
                .value()
                .attr("src")?
                .split('?')
                .next()?
                .to_string();
            let breed = Breed {
                name:        name.clone(),
                details_url: format!("https://www.purina.co.uk{details_url}"),
                image_url:   format!("https://www.purina.co.uk{image_url}"),
            };

            Some(Breed {
                name,
                details_url: format!("https://www.purina.co.uk{details_url}"),
                image_url: format!("https://www.purina.co.uk{image_url}"),
            })
        })
        .collect()
}


fn extract_akc_breed_details(tab: &Arc<Tab>, breed: &Breed) -> Result<BreedDetails, anyhow::Error> {
    let desc_container = tab.wait_for_element(".breed-page__about__read-more__text")?;
    let desc_lines = desc_container.find_elements("p")?;
    let desc = if desc_lines.is_empty() {
        desc_container.get_inner_text()?.trim().to_string()
    } else {
        desc_lines
            .iter()
            .map(|element| element.get_inner_text().unwrap().trim().to_string())
            .filter(|line| !line.is_empty())
            .collect::<Vec<_>>()
            .join("\n")
    };

    let basic_details_container = tab.wait_for_element(".breed-page__hero__overview__icon-block-wrap")?;
    let basic_details: BTreeMap<String, String> = basic_details_container
        .find_elements(".breed-page__hero__overview__icon-block")?
        .iter()
        .flat_map(|element| {
            let title = element
                .find_element("h3")?
                .get_inner_text()?
                .to_lowercase()
                .replace(' ', "_");
            let value = element.find_element("p")?.get_inner_text()?;
            Ok::<_, anyhow::Error>((title, value))
        })
        .collect();

    // // * Find "li.tabs__single-tab" element (there are a few), which has a
    // // * "#tab__breed-page__traits__all" child. Then click on it.
    // tab.wait_for_element("li.tabs__single-tab:has(#tab__breed-page__traits__all)")?.click()?;
    let advanced_details_container = tab.wait_for_element("#breed-page__traits__all")?;
    let advanced_details: BTreeMap<String, String> = advanced_details_container
        .find_elements(".breed-trait-group__trait")?
        .iter()
        .flat_map(|element| {
            let title = element
                .find_element("h4.breed-trait-group__header")?
                .get_inner_text()?
                .to_lowercase()
                .replace(' ', "_");
            let is_percent = !element
                .find_elements(".breed-trait-score__score-unit")?
                .is_empty();
            let value = if is_percent {
                (element
                    .find_elements(".breed-trait-score__score-unit--filled")?
                    .len() as f32
                    / 5.0)
                    .to_string()
            } else if let Ok(el) = element.find_element(".breed-trait-score__choice--selected > span") {
                el.get_inner_text().unwrap_or_default().to_lowercase()
            } else {
                String::new()
            };
            Ok::<_, anyhow::Error>((title, value))
        })
        .collect();

    Ok(BreedDetails {
        name: breed.name.clone(),
        image_url: breed.image_url.clone(),
        description: desc,
        basic_details,
        advanced_details,
    })
}

fn extract_purina_breed_details(body: &Html, breed: &Breed) -> Option<BreedDetails> {
    // debug!(LOG, "[Purina] Scraping details for {} from {}", breed.name, &breed.details_url,);
    // let now = Instant::now();
    let description = body
        .select(&Selector::parse(".field--name-field-nppe-bs-description").unwrap())
        .next()?
        .text()
        .next()?
        .trim()
        .to_string();
    let basic_details_selector = Selector::parse(".field--name-field-key-facts").unwrap();
    let basic_details = body
        .select(&basic_details_selector)
        .next()?
        .text()
        .next()?
        .trim()
        .to_string();
    let advanced_details_selector = Selector::parse(".field--name-field-c-subitems").unwrap();
    let advanced_details = body
        .select(&advanced_details_selector)
        .next()?
        .text()
        .next()?
        .trim()
        .to_string();
    // debug!(LOG, "Scraped {} [{:.0?}].", breed.name, now.elapsed());
    // debug!(LOG, "Description: {}", description);
    // debug!(LOG, "Basic details: {:?}", basic_details);
    // debug!(LOG, "Advanced details: {:?}\n", advanced_details);

    Some(BreedDetails {
        name: breed.name.clone(),
        image_url: breed.image_url.clone(),
        description,
        basic_details: BTreeMap::new(),
        advanced_details: BTreeMap::new(),
    })
}

fn save_breeds_to_parquet(breeds: Vec<BreedDetails>) {
    use std::fs::File;

    use polars::prelude::*;

    let mut names = Vec::new();
    let mut descriptions = Vec::new();
    let mut image_urls = Vec::new();
    let mut basic_details = Vec::new();
    let mut advanced_details = Vec::new();

    for breed in breeds {
        names.push(breed.name);
        descriptions.push(breed.description);
        image_urls.push(breed.image_url);
        basic_details.push(
            breed
                .basic_details
                .iter()
                .map(|(k, v)| format!("{k}: {v}"))
                .collect::<Vec<_>>()
                .join(", "),
        );
        advanced_details.push(
            breed
                .advanced_details
                .iter()
                .map(|(k, v)| format!("{k}: {v}"))
                .collect::<Vec<_>>()
                .join(", "),
        );
    }

    let mut df = df! {
        "name" => names,
        "description" => descriptions,
        "image_url" => image_urls,
        "basic_details" => basic_details,
        "advanced_details" => advanced_details,
    }
    .unwrap();

    let mut file = File::create("breeds.parquet").unwrap();
    ParquetWriter::new(&mut file).finish(&mut df).unwrap();
}
